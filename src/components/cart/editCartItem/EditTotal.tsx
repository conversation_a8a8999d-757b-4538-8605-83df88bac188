import { ActiveCartItem } from "@/types/CartType";
import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { Edit } from "lucide-react";
import ListCartFees from "./ListCartFees";
import UpdateCartItemFee from "./UpdateCartItemFee";
import { EditFeeProps } from "@/hooks/api/useCart";
import AddCartItemFee from "./AddCartItemFee";
import { convertToDollar } from "@/components/fees/modal/feeHelper";

export default function EditTotal({
  cartItem,
  className,
  canEdit = true,
  onFeesUpdated,
}: {
  cartItem: ActiveCartItem;
  className?: string;
  canEdit?: boolean;
  onFeesUpdated?: () => void;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [editFee, setEditFee] = useState<EditFeeProps | null>(null);

  if (!canEdit) {
    return (
      <div className={cn("line-clamp-1 text-xl font-semibold", className)}>
        {convertToDollar(cartItem.total)}
      </div>
    );
  }

  const handleClose = () => {
    setIsOpen(false);
    setEditFee(null);
  };

  return (
    <Dialog
      onOpenChange={(open) => {
        if (!open) handleClose();
        else setIsOpen(true);
      }}
      open={isOpen}
    >
      <DialogTrigger asChild>
        <button
          className={cn(
            "flex items-center gap-2 rounded-lg text-xl font-semibold text-gray-800 transition hover:text-blue-600",
            className,
          )}
          type="button"
        >
          <Edit className="h-5 w-5" />
        </button>
      </DialogTrigger>
      <DialogContent className="rounded-lg bg-white p-6 shadow-lg sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold text-gray-900">
            Cart Item Total
          </DialogTitle>
        </DialogHeader>

        {!editFee ? (
          <ListCartFees cartItem={cartItem} setEditFee={setEditFee} />
        ) : editFee.feeId ? (
          <UpdateCartItemFee
            editFee={editFee}
            setEditFee={setEditFee}
            cartItemId={cartItem.cartItemId}
            onFeesUpdated={onFeesUpdated}
          />
        ) : (
          <AddCartItemFee
            editFee={editFee}
            setEditFee={setEditFee}
            cartItemId={cartItem.cartItemId}
            onFeesUpdated={onFeesUpdated}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}


export const useDollarInput = (initialValue: number | string = 0) => {
  const [value, setValue] = useState<number | string>(initialValue);

  const formatCurrency = (value: number | string): string => {
    const numericValue =
      parseFloat(value.toString().replace(/[^0-9.-]+/g, "")) || 0;
    return numericValue.toLocaleString("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let rawValue = e.target.value;

    // Allow only numbers and a single decimal
    rawValue = rawValue.replace(/[^0-9.]/g, "");

    // Prevent multiple decimals
    if (rawValue.split(".").length > 2) return;

    // Limit to two decimal places
    const [integer, decimals] = rawValue.split(".");
    if (decimals && decimals.length > 2) {
      rawValue = `${integer}.${decimals.slice(0, 2)}`;
    }

    setValue(rawValue);
  };

  return {
    value,
    formattedValue: formatCurrency(value),
    onChange: handleChange,
  };
};
