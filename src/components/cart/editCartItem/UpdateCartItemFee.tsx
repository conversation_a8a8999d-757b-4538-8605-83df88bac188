import { useState } from "react";
import { DialogFooter } from "@/components/ui/dialog";
import { Button } from "../../ui/button";
import { ArrowLeft } from "lucide-react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "../../ui/label";
import { useDollarInput } from "./EditTotal";
import {
  EditFeeProps,
  useRemoveFeeFromCartItem,
  useUpdateFeeAmount,
} from "@/hooks/api/useCart";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { useMyCart } from "@/hooks/useMyCart";

const UpdateCartItemFee = ({
  cartItemId,
  editFee,
  setEditFee,
  onFeesUpdated,
}: {
  cartItemId: number;
  editFee: EditFeeProps;
  setEditFee: (editFee: EditFeeProps | null) => void;
  onFeesUpdated?: () => void;
}) => {
  const updateFee = useUpdateFeeAmount(cartItemId);
  const deleteMutation = useRemoveFeeFromCartItem(cartItemId);

  const [_, setToast] = useAtom(toastAtom);
  const [calculationType, setCalculationType] = useState("fixed");
  const [adjustmentType, setAdjustmentType] = useState(
    editFee.feeAmount !== null && editFee.feeAmount < 0 ? "discount" : "fee",
  );
  const [adjustmentReason, setAdjustmentReason] = useState("");
  const { value, onChange } = useDollarInput(
    editFee.feeAmount !== null && editFee.feeAmount < 0
      ? editFee.feeAmount * -1
      : editFee.feeAmount ?? 0,
  );
  const { cartRefetch } = useMyCart();

  const handleClose = () => {
    setEditFee(null);
    setAdjustmentReason("");
  };

  const handleSave = () => {
    const amount =
      adjustmentType === "discount" ? Number(value) * -1 : Number(value);
    updateFee.mutate(
      {
        feeId: editFee.feeId ?? "",
        feeAmount: amount,
        reason: adjustmentReason,
      },
      {
        onSuccess: () => {
          setToast({
            label: "Success",
            message: "Successfully updated fee amount",
            status: "success",
          });
          if (onFeesUpdated) {
            onFeesUpdated();
          } else {
            cartRefetch();
          }
          setEditFee(null);
        },
        onError: () => {
          setToast({
            label: "Error",
            message: "Error updating fee amount",
            status: "error",
          });
        },
      },
    );
  };

  const handleDelete = (feeId: string) => {
    deleteMutation.mutate(
      {
        feeId,
        reason: adjustmentReason,
      },
      {
        onSuccess: () => {
          setToast({
            label: "Success",
            message: "Successfully deleted fee",
            status: "success",
          });
          if (onFeesUpdated) {
            onFeesUpdated();
          } else {
            cartRefetch();
          }
          setEditFee(null);
        },
        onError: () => {
          setToast({
            label: "Error",
            message: "Error deleting fee",
            status: "error",
          });
        },
      },
    );
  };

  console.log(editFee?.label);

  return (
    <>
      <div className="space-y-8">
        <button
          className="flex items-center gap-2 text-blue-600"
          onClick={handleClose}
        >
          <ArrowLeft className="size-4" />
          Back
        </button>

        <div>
          <div className="text-sm font-medium text-gray-700">Name</div>
          <div className="text-xl font-medium">
            {editFee?.label ?? "No Name"}
          </div>
          <div className="text-xs italic">{editFee?.feeCode ?? "No Code"}</div>
        </div>

        <div>
          <Label
            htmlFor="adjustmentType"
            className="text-sm font-medium text-gray-700"
          >
            Adjustment Type
          </Label>
          <RadioGroup
            defaultValue={adjustmentType}
            onValueChange={setAdjustmentType}
            className="mt-3 flex items-center gap-6"
          >
            {["discount", "fee"].map((type) => (
              <div key={type} className="flex items-center space-x-2">
                <RadioGroupItem
                  value={type}
                  id={type}
                  className="focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                />
                <Label htmlFor={type} className="capitalize text-gray-800">
                  {type}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>

        {/* Removed until i decide how i want to handle percentages. percentage of what? -- Sean B */}
        {/* <div>
          <Label
            htmlFor="calculationType"
            className="text-sm font-medium text-gray-700"
          >
            Calculation Type
          </Label>
          <RadioGroup
            defaultValue={calculationType}
            onValueChange={setCalculationType}
            className="mt-3 flex items-center gap-6"
          >
            {["fixed", "percentage"].map((type) => (
              <div key={type} className="flex items-center space-x-2">
                <RadioGroupItem
                  value={type}
                  id={type}
                  className="focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                />
                <Label htmlFor={type} className="capitalize text-gray-800">
                  {type}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div> */}

        <div>
          <Label
            htmlFor="adjustment"
            className="text-sm font-medium text-gray-700"
          >
            Adjustment Amount
          </Label>
          <div className="mt-2 flex items-center gap-2">
            {/* Show $ if fixed */}
            {
              <span className="translate-y-0 text-3xl font-semibold text-gray-700">
                {calculationType === "fixed" ? "$" : ""}
              </span>
            }
            {/* Show - if discount */}
            {
              <span className="translate-y-0 text-3xl font-semibold text-gray-700">
                {adjustmentType === "discount" ? "-" : ""}
              </span>
            }
            <input
              type="text"
              id="adjustment"
              className=" w-full max-w-[180px] rounded-lg border border-gray-300 p-3 text-2xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={value}
              onChange={onChange}
              placeholder="$0.00"
              inputMode="decimal"
            />
            {/* Show % if percentage */}
            {/* {
              <span className="translate-y-2 text-3xl font-semibold text-gray-700">
                {calculationType === "percentage" ? "%" : ""}
              </span>
            } */}
          </div>
        </div>

        <div>
          <Label htmlFor="reason" className="text-sm font-medium text-gray-700">
            Reason for Change
          </Label>
          <textarea
            id="reason"
            className="mt-2 w-full rounded-lg border border-gray-300 p-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={adjustmentReason}
            onChange={(e) => setAdjustmentReason(e.target.value)}
            placeholder="Enter the reason for the adjustment..."
          />
        </div>
      </div>
      <DialogFooter className="mt-6 flex justify-end space-x-3">
        {editFee.feeId && (
          <Button
            variant="destructive"
            type="button"
            className="mr-auto"
            disabled={updateFee.isLoading || deleteMutation.isLoading}
            onClick={() => {
              handleDelete(editFee.feeId ?? "");
            }}
          >
            {deleteMutation.isLoading ? "Deleting..." : "Delete Fee"}
          </Button>
        )}
        <Button variant="outline" type="button" onClick={handleClose}>
          Cancel
        </Button>
        <Button
          variant="primary"
          type="button"
          onClick={handleSave}
          disabled={updateFee.isLoading || deleteMutation.isLoading}
        >
          {updateFee.isLoading ? "Saving..." : "Save"}
        </Button>
      </DialogFooter>
    </>
  );
};

export default UpdateCartItemFee;
