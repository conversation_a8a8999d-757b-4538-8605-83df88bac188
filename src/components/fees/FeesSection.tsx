import { formatDate } from "@/components/license/licenseHelper";
import React, { useEffect } from "react";
import { Fi<PERSON>heck, FiX } from "react-icons/fi";
import FeeR<PERSON>eipt from "./FeeReceipt";
import {
  useGetLicenseFeeTable,
  useGetResidentLicenseFeeTable,
} from "@/hooks/api/useLicense";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import EditTotal from "../cart/editCartItem/EditTotal";
import { ActiveCartFee, ActiveCartItem } from "@/types/CartType";
import { useMyCart } from "@/hooks/useMyCart";
import { useParams } from 'next/navigation'

const formatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
});

export default function FeesSection({ fees, refetch }: { fees: any, refetch: any }) {
  const feesSorted = fees?.fees?.sort((a: any, b: any) => {
    return (
      new Date(b.feePaidDate).getTime() - new Date(a.feePaidDate).getTime()
    );
  });

  const {
    data: clerkFeeTable,
    isLoading: clerkFeeTableLoading,
    isError: clerkFeeTableError,
    error: clerkError,
  } = useGetLicenseFeeTable();
  const {
    data: residentFeeTable,
    isLoading: residentFeeTableLoading,
    isError: residentFeeTableError,
    error: residentError,
  } = useGetResidentLicenseFeeTable();
  const params = useParams();
  const entityId = params.entityId;

  const { hasPermissions } = useMyProfile();
  const admin = hasPermissions(["super-admin"]);
  const { cartSummary, cartIsFetching, cartLoading, cartRefetch } = useMyCart();

  const data = admin ? clerkFeeTable : residentFeeTable;
  const isLoading = admin ? clerkFeeTableLoading : residentFeeTableLoading;
  const isError = admin ? clerkFeeTableError : residentFeeTableError;
  const error = admin ? clerkError : residentError;

  const totalAmount = fees.totalAmount
    ? formatter.format(fees.totalAmount)
    : "No Total Amount";
  const totalAmountPaid = fees.totalPaidAmount
    ? formatter.format(fees.totalPaidAmount)
    : "No Total Amount Paid";
  const totalDiscountAmount = fees.totalDiscountAmount
    ? formatter.format(fees.totalDiscountAmount)
    : "$0.00";
  const totalSubtotalAmount = fees.totalSubtotal
    ? formatter.format(fees.totalSubtotal)
    : "$0.00";
  const realCartItem: ActiveCartItem | undefined = cartSummary?.items.find((x) => x.itemId === entityId);




  if (isLoading || cartLoading) return <div>Loading...</div>;

  if (isError) {
    console.log(error);
    return <div>Error loading fee data</div>;
  }


  return (
    <div
      key={fees.feeCode}
      className="max-w-2xl rounded p-2 shadow hover:bg-neutral-50 hover:shadow-xl"
    >
      <div className="flex items-center justify-between">
       <h2 className="font-semibold">
         Fee Processed Date: {formatDate(fees.processedDate)}
       </h2>
       {!fees.processedDate && (
         <EditTotal cartItem={realCartItem} />
       )}
     </div>
      <p className="mb-3 text-sm">
        License Dates: {formatDate(fees.validFrom)} - {formatDate(fees.validTo)}
      </p>
      {feesSorted.map(
        (fee: { feeCode: string; feeStatus: string; feeAmount: number }) => {
          const feeAmount = fee.feeAmount
            ? formatter.format(fee.feeAmount)
            : "No Fee Amount";

          const feeLabel =
            data.find((item: { key: string }) => item.key === fee.feeCode)
              ?.feeName || "Uknown Fee";

          return (
            <div key={fee.feeCode} className="flex justify-between">
              <h3 className="text-sm text-neutral-700">{feeLabel}</h3>
              <div className="flex items-center gap-2">
                <p className="text-sm">
                  {fee.feeStatus === "paid" ? (
                    <FiCheck className="text-emerald-500" />
                  ) : (
                    <FiX className="text-red-500" />
                  )}
                </p>
                <p className="text-sm">{feeAmount}</p>
              </div>
            </div>
          );
        },
      )}
      <p className="mt-4 text-right text-sm font-medium text-neutral-700">
        Subtotal: {totalSubtotalAmount}
      </p>
      <p className="text-right text-sm font-medium text-neutral-700">
        Discounts Applied: {totalDiscountAmount}
      </p>
      <p className="mt-2 text-right text-xl font-semibold">
        License Total: {totalAmount}
      </p>
      <p className="mt-6 border p-2 text-center text-sm font-semibold text-neutral-500">
        Total Amount Paid: {totalAmountPaid}
      </p>
      {fees.orderId ? (
        <FeeReceipt orderId={fees.orderId} />
      ) : (
        <div className="mt-1 text-center text-red-500">
          No Receipt Available
        </div>
      )}
    </div>
  );
}
